#!/usr/bin/env python3
"""
dicom_watermark.py
------------------
DICOM 隐形水印工具（右下角 ROI，像素 LSB）。

功能：
- 在 DICOM 图像像素中写入隐形水印（修改 ROI 内像素的最低有效位）
- 从 DICOM 中读取隐形水印
- 支持多帧（默认写在最后一帧）
- 支持简单 XOR 密钥混淆
- 自动标记 DICOM 为 DERIVED 图像

依赖：
    pip install pydicom numpy
可选（如需处理 JPEG/J2K 压缩图像）：
    pip install pylibjpeg pylibjpeg-libjpeg pydicom[gdcm]
"""

import argparse
import zlib
import struct
from typing import Tuple, Optional

import numpy as np
import pydicom
from pydicom.dataset import Dataset

MAGIC = b"DWM1"  # 水印魔数
HEADER_LEN = 12  # 4字节魔数 + 4字节长度 + 4字节CRC


# ----------------- 工具函数 -----------------
def _ensure_uncompressed(ds: Dataset) -> Dataset:
    """解压压缩传输语法（需要 pylibjpeg 或 gdcm 插件）"""
    ts = ds.file_meta.TransferSyntaxUID
    if ts.is_compressed:
        ds.decompress()
    return ds


def _get_array_and_dtype(ds: Dataset) -> Tuple[np.ndarray, np.dtype, bool]:
    """获取像素数组 + dtype"""
    arr = ds.pixel_array
    bits_alloc = int(ds.get("BitsAllocated", 16))
    signed = int(ds.get("PixelRepresentation", 0)) == 1
    if bits_alloc == 8:
        dtype = np.int8 if signed else np.uint8
    elif bits_alloc == 16:
        dtype = np.int16 if signed else np.uint16
    elif bits_alloc == 32:
        dtype = np.int32 if signed else np.uint32
    else:
        dtype = np.uint16
    if arr.dtype != dtype:
        arr = arr.astype(dtype, copy=True)
    return arr, dtype, signed


def _to_unsigned_view(a: np.ndarray) -> np.ndarray:
    """把有符号视图成无符号，方便位运算"""
    if a.dtype == np.int16:
        return a.view(np.uint16)
    if a.dtype == np.int32:
        return a.view(np.uint32)
    if a.dtype == np.int8:
        return a.view(np.uint8)
    return a


def _from_unsigned_view(u: np.ndarray, signed_dtype) -> np.ndarray:
    """还原为原始 signed dtype"""
    if signed_dtype == np.int16:
        return u.view(np.int16)
    if signed_dtype == np.int32:
        return u.view(np.int32)
    if signed_dtype == np.int8:
        return u.view(np.int8)
    return u


def _bitwise_not_mask(mask, dtype):
    """确保 ~mask 落在 dtype 对应的无符号范围内"""
    if dtype == np.uint8:
        return (~mask) & 0xFF
    elif dtype == np.uint16:
        return (~mask) & 0xFFFF
    elif dtype == np.uint32:
        return (~mask) & 0xFFFFFFFF
    else:
        bits = np.dtype(dtype).itemsize * 8
        return (~mask) & ((1 << bits) - 1)


def _pack_payload(text: str, key: Optional[str]) -> bytes:
    data = text.encode("utf-8")
    crc = zlib.crc32(data)
    header = MAGIC + struct.pack(">I", len(data)) + struct.pack(">I", crc)
    payload = header + data
    if key:
        k = key.encode("utf-8")
        payload = bytes(b ^ k[i % len(k)] for i, b in enumerate(payload))
    return payload


def _unpack_payload(buf: bytes, key: Optional[str]) -> str:
    data = buf
    if key:
        k = key.encode("utf-8")
        data = bytes(b ^ k[i % len(k)] for i, b in enumerate(buf))
    if len(data) < HEADER_LEN:
        raise ValueError("缓冲区不足")
    magic = data[:4]
    if magic != MAGIC:
        raise ValueError("魔数不匹配，可能未嵌入水印或 key 错误")
    length = struct.unpack(">I", data[4:8])[0]
    crc = struct.unpack(">I", data[8:12])[0]
    msg = data[12:12+length]
    if len(msg) != length:
        raise ValueError("消息长度不完整")
    if zlib.crc32(msg) != crc:
        raise ValueError("CRC 校验失败")
    return msg.decode("utf-8")


def _roi_coords(h: int, w: int, ratio: float, needed_bits: int, bits_per_pixel: int):
    """计算 ROI 坐标 (y0, x0, y1, x1)"""
    if ratio > 0:
        rh = max(1, int(round(h * ratio)))
        rw = max(1, int(round(w * ratio)))
        return h - rh, w - rw, h, w
    # auto mode: 最小 ROI
    pixels_needed = (needed_bits + bits_per_pixel - 1) // bits_per_pixel
    side = int(np.ceil(np.sqrt(pixels_needed)))
    rh = min(h, side)
    rw = min(w, int(np.ceil(pixels_needed / rh)))
    return h - rh, w - rw, h, w


# ----------------- 主逻辑 -----------------
def embed(in_path: str, out_path: str, text: str, bits: int = 1, roi_ratio: float = 0.2, key: Optional[str] = None):
    ds = pydicom.dcmread(in_path)
    ds = _ensure_uncompressed(ds)
    arr, dtype, signed = _get_array_and_dtype(ds)

    if arr.ndim == 3 and arr.shape[0] > 1:
        vol = arr
        img = vol[-1].copy()
        target = img
    else:
        vol = None
        target = arr.copy()

    h, w = target.shape[-2], target.shape[-1]
    payload = _pack_payload(text, key)
    needed_bits = len(payload) * 8

    y0, x0, y1, x1 = _roi_coords(h, w, roi_ratio if roi_ratio > 0 else -1.0, needed_bits, bits)
    roi = target[y0:y1, x0:x1]

    capacity = roi.size * bits
    if capacity < needed_bits:
        raise ValueError(f"ROI 容量不足，需要 {needed_bits} bits，当前 {capacity} bits")

    u = _to_unsigned_view(roi)
    flat = u.ravel()
    bitstream = np.unpackbits(np.frombuffer(payload, dtype=np.uint8), bitorder="big")

    if bits == 1:
        mask = np.uint16(1) if u.dtype == np.uint16 else np.uint32(1) if u.dtype == np.uint32 else np.uint8(1)
        inv_mask = _bitwise_not_mask(mask, flat.dtype)
        flat[:bitstream.size] = (flat[:bitstream.size] & inv_mask) | bitstream.astype(flat.dtype)
    else:
        total_pixels = int(np.ceil(bitstream.size / bits))
        padded = np.zeros(total_pixels * bits, dtype=np.uint8)
        padded[:bitstream.size] = bitstream
        chunks = padded.reshape(-1, bits)
        weights = 1 << np.arange(bits-1, -1, -1, dtype=np.uint8)
        vals = (chunks * weights).sum(axis=1).astype(flat.dtype)
        mask = (1 << bits) - 1
        inv_mask = _bitwise_not_mask(mask, flat.dtype)
        flat[:total_pixels] = (flat[:total_pixels] & inv_mask) | vals

    u = flat.reshape(u.shape)
    new_roi = _from_unsigned_view(u, roi.dtype)
    target[y0:y1, x0:x1] = new_roi

    if vol is not None:
        vol[-1] = target
        new_arr = vol
    else:
        new_arr = target

    ds.PixelData = new_arr.tobytes()
    ds.ImageType = list(ds.get("ImageType", [])) or ["", "", "DERIVED"]
    ds.ImageType[2] = "DERIVED"
    ds.DerivationDescription = f"Invisible watermark embedded ROI=({y0},{x0}), bits={bits}, text_len={len(text)}"

    ds.save_as(out_path)
    print(f"✅ 已写入：{out_path}")


def read(in_path: str, bits: int = 1, roi_ratio: float = 0.2, key: Optional[str] = None) -> str:
    ds = pydicom.dcmread(in_path)
    ds = _ensure_uncompressed(ds)
    arr, dtype, signed = _get_array_and_dtype(ds)

    img = arr[-1] if arr.ndim == 3 and arr.shape[0] > 1 else arr
    h, w = img.shape[-2], img.shape[-1]

    y0, x0, y1, x1 = _roi_coords(h, w, roi_ratio if roi_ratio > 0 else -1.0, HEADER_LEN*8, bits)
    roi = img[y0:y1, x0:x1]
    u = _to_unsigned_view(roi).ravel()

    def extract_bits(u_flat, bits_per_pixel, count_bits):
        if bits_per_pixel == 1:
            mask = np.uint16(1) if u_flat.dtype == np.uint16 else np.uint32(1) if u_flat.dtype == np.uint32 else np.uint8(1)
            vals = (u_flat[:count_bits] & mask).astype(np.uint8)
            return vals
        else:
            mask = (1 << bits_per_pixel) - 1
            vals = (u_flat[: int(np.ceil(count_bits / bits_per_pixel))] & mask).astype(np.uint8)
            bits_list = []
            for v in vals:
                for k in range(bits_per_pixel-1, -1, -1):
                    bits_list.append((v >> k) & 1)
            return np.array(bits_list[:count_bits], dtype=np.uint8)

    hb = extract_bits(u, bits, HEADER_LEN*8)
    header_bytes = np.packbits(hb, bitorder="big").tobytes()
    tmp = header_bytes
    if key:
        k = key.encode("utf-8")
        tmp = bytes(b ^ k[i % len(k)] for i, b in enumerate(header_bytes))

    if tmp[:4] != MAGIC:
        raise ValueError("未检测到有效水印")

    msg_len = struct.unpack(">I", tmp[4:8])[0]
    total_bits = (HEADER_LEN + msg_len) * 8
    bits_all = extract_bits(u, bits, total_bits)
    data = np.packbits(bits_all, bitorder="big").tobytes()
    text = _unpack_payload(data, key)
    print("✅ 读取成功")
    return text


# ----------------- CLI -----------------
def main():
    ap = argparse.ArgumentParser(description="DICOM 隐形水印工具")
    sub = ap.add_subparsers(dest="cmd", required=True)

    ap_e = sub.add_parser("embed", help="写入水印")
    ap_e.add_argument("in_dcm")
    ap_e.add_argument("out_dcm")
    ap_e.add_argument("--text", required=True)
    ap_e.add_argument("--bits", type=int, default=1)
    ap_e.add_argument("--roi-ratio", type=float, default=0.2)
    ap_e.add_argument("--key", default=None)

    ap_r = sub.add_parser("read", help="读取水印")
    ap_r.add_argument("in_dcm")
    ap_r.add_argument("--bits", type=int, default=1)
    ap_r.add_argument("--roi-ratio", type=float, default=0.2)
    ap_r.add_argument("--key", default=None)

    args = ap.parse_args()
    if args.cmd == "embed":
        embed(args.in_dcm, args.out_dcm, args.text, bits=args.bits, roi_ratio=args.roi_ratio, key=args.key)
    elif args.cmd == "read":
        txt = read(args.in_dcm, bits=args.bits, roi_ratio=args.roi_ratio, key=args.key)
        print("水印内容：", txt)


if __name__ == "__main__":
    main()
